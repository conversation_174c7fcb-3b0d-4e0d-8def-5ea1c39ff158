package com.siemens.spine.logic.validator.query.handler;

import com.siemens.spine.logic.validator.query.QueryType;
import com.siemens.spine.logic.validator.query.QueryValidationContext;
import com.siemens.spine.logic.validator.query.ValidationResult;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.update.Update;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Named;

@ApplicationScoped
@Named("queryTypeValidationHandler")
@Slf4j
public class QueryTypeValidationHandler extends AbstractQueryValidationHandler {

    @Override
    protected ValidationResult doValidate(QueryValidationContext context) {
        Statement statement = context.getParsedStatement();
        if (statement == null) {
            try {
                statement = CCJSqlParserUtil.parse(context.getQueryString());
            } catch (JSQLParserException e) {
                log.error("Cannot parse query: {}", e.getMessage());
                return ValidationResult.invalidSyntax("Cannot parse query");
            }
        }

        QueryType detectedType = detectQueryType(statement);

        if (detectedType == null) {
            log.error("Unsupported query type: {}", statement);
            return ValidationResult.invalidFormat("Unsupported query type");
        }

        if (context.getQueryType() != null && context.getQueryType() != detectedType) {
            log.error("Query type mismatch. Expected: {}, Actual: {}", context.getQueryType(), detectedType);
            return ValidationResult.invalidFormat(
                    "Query type mismatch. Expected: " + context.getQueryType() + ", Actual: " + detectedType);
        }

        return ValidationResult.success();
    }

    private QueryType detectQueryType(Statement statement) {
        if (statement instanceof Select) {
            return QueryType.SELECT;
        }
        if (statement instanceof Insert) {
            log.warn("INSERT statement detected");
            return null;
        }
        if (statement instanceof Update) {
            log.warn("UPDATE statement detected");
            return null;
        }
        if (statement instanceof Delete) {
            log.warn("DELETE statement detected");
            return null;
        }
        return null;
    }

}