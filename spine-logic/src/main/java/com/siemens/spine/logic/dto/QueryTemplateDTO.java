package com.siemens.spine.logic.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.sql.Timestamp;

@Data
public class QueryTemplateDTO {

    private Long id;

    @NotNull
    private String name;

    @NotNull
    private String query;

    private String description;

    private boolean requiresPagination = false;

    @JsonIgnore
    private Timestamp sysModDate;

    @JsonIgnore
    private String createdBy;

    @JsonIgnore
    private String modifiedBy;

    @JsonIgnore
    private Timestamp sysCreateDate;

}
