package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.entity.QueryTemplateEntity;
import com.siemens.spine.logic.dto.QueryTemplateDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2025/06/09
 */

@Mapper
public interface DynamicQueryMapper {

    DynamicQueryMapper INSTANCE = Mappers.getMapper(DynamicQueryMapper.class);

    @Mapping(target = "sqlTemplate", source = "query")
    @Mapping( target = "templateName", source = "name")
    QueryTemplateEntity toQueryTemplateEntity(QueryTemplateDTO dto);

    @Mapping(target = "query", source = "sqlTemplate")
    @Mapping(target = "name", source = "templateName")
    QueryTemplateDTO toQueryTemplateDTO(QueryTemplateEntity dto);

}
