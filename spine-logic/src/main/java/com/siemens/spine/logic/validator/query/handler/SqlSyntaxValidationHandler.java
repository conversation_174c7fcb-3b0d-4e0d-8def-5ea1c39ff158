package com.siemens.spine.logic.validator.query.handler;

import com.siemens.spine.logic.validator.query.QueryValidationContext;
import com.siemens.spine.logic.validator.query.ValidationResult;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParser;
import net.sf.jsqlparser.parser.ParseException;
import net.sf.jsqlparser.parser.StreamProvider;
import net.sf.jsqlparser.parser.feature.Feature;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.util.validation.Validation;
import net.sf.jsqlparser.util.validation.ValidationError;
import net.sf.jsqlparser.util.validation.ValidationException;
import net.sf.jsqlparser.util.validation.feature.DatabaseType;
import org.apache.commons.collections4.CollectionUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Named;
import java.io.StringReader;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@ApplicationScoped
@Named("sqlSyntaxValidationHandler")
@Slf4j
public class SqlSyntaxValidationHandler extends AbstractQueryValidationHandler {

    @Override
    protected ValidationResult doValidate(QueryValidationContext context) {
        try {
            Statement statement = parseWithFeatures(context);
            ValidationResult validationResult = performAdditionalValidation(context);
            if (!validationResult.valid()) {
                return validationResult;
            }

            context.setParsedStatement(statement);
            return ValidationResult.success();
        } catch (JSQLParserException e) {
            log.error("SQL syntax error for query '{}': {}", context.getQueryString(), e.getMessage());
            return ValidationResult.invalidSyntax("Invalid SQL syntax: " + e.getMessage());
        }
    }

    /**
     * Performs additional validation using JSQLParser's validation framework
     *
     * @param context The query validation context
     * @return ValidationResult indicating success or failure with error details
     */
    private ValidationResult performAdditionalValidation(QueryValidationContext context) {
        try {
            List<ValidationError> errors = Validation.validate(
                    Collections.singletonList(DatabaseType.POSTGRESQL),
                    context.getQueryString()
            );

            if (CollectionUtils.isNotEmpty(errors)) {
                String errorMessages = errors.stream()
                        .map(ValidationError::getErrors)
                        .flatMap(errorsSet -> errorsSet.stream().map(ValidationException::getMessage))
                        .collect(Collectors.joining("; "));
                log.warn("SQL validation errors found for query '{}': {}", context.getQueryString(), errorMessages);
                return ValidationResult.invalidSyntax("SQL syntax errors: " + errorMessages);
            }

            return ValidationResult.success();
        } catch (Exception e) {
            log.error("Unexpected error during additional validation for query '{}': {}",
                    context.getQueryString(), e.getMessage());
            return ValidationResult.invalidSyntax("Validation error: " + e.getMessage());
        }
    }

    /**
     * Parses SQL statement with configured features for PostgreSQL compatibility
     *
     * @param context The query validation context containing the SQL string
     * @return Parsed Statement object
     * @throws JSQLParserException if parsing fails
     */
    private Statement parseWithFeatures(QueryValidationContext context) throws JSQLParserException {
        try {
            CCJSqlParser parser = new CCJSqlParser(new StreamProvider(new StringReader(context.getQueryString())));
            parser.withAllowComplexParsing();
            configureParserFeatures(parser);
            return parser.Statement();
        } catch (ParseException e) {
            throw new JSQLParserException("Parser configuration error: " + e.getMessage(), e);
        }
    }

    /**
     * Configures parser features for PostgreSQL compatibility
     *
     * @param parser The CCJSqlParser instance to configure
     */
    private void configureParserFeatures(CCJSqlParser parser) {
        // Basic SELECT features
        parser.withFeature(Feature.select, true);
        parser.withFeature(Feature.selectGroupBy, true);
        parser.withFeature(Feature.selectHaving, true);
        parser.withFeature(Feature.selectUnique, true);

        // Ordering and limiting features
        parser.withFeature(Feature.desc, true);
        parser.withFeature(Feature.orderBy, true);
        parser.withFeature(Feature.limit, true);
        parser.withFeature(Feature.limitOffset, true);
        parser.withFeature(Feature.limitAll, true);

        // JOIN features
        parser.withFeature(Feature.join, true);
        parser.withFeature(Feature.joinInner, true);
        parser.withFeature(Feature.joinLeft, true);
        parser.withFeature(Feature.joinRight, true);
        parser.withFeature(Feature.joinFull, true);
        parser.withFeature(Feature.joinCross, true);

        // PostgreSQL-specific syntax
        parser.withFeature(Feature.allowPostgresSpecificSyntax, true);
    }

}