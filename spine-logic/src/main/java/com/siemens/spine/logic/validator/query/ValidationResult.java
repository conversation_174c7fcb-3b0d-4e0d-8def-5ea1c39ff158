package com.siemens.spine.logic.validator.query;

/**
 * Validation result for query validation
 *
 * <AUTHOR>
 * @version 1.0
 * @since 26/06/2025
 */
public record ValidationResult(boolean valid, String errorMessage, ValidationErrorType errorType) {

    public static ValidationResult success() {
        return new ValidationResult(true, null, null);
    }

    public static ValidationResult failure(String message, ValidationErrorType type) {
        return new ValidationResult(false, message, type);
    }

    public static ValidationResult unauthorizedOperation(String message) {
        return failure(message, ValidationErrorType.UNAUTHORIZED_OPERATION);
    }

    public static ValidationResult invalidSyntax(String message) {
        return failure(message, ValidationErrorType.INVALID_SYNTAX);
    }

    public static ValidationResult securityViolation(String message) {
        return failure(message, ValidationErrorType.SECURITY_VIOLATION);
    }

    public static ValidationResult invalidParameter(String message) {
        return failure(message, ValidationErrorType.INVALID_PARAMETER);
    }

    public static ValidationResult missingParameter(String message) {
        return failure(message, ValidationErrorType.MISSING_PARAMETER);
    }

    public static ValidationResult missingField(String message) {
        return failure(message, ValidationErrorType.MISSING_FIELD);
    }

    public static ValidationResult invalidFormat(String message) {
        return failure(message, ValidationErrorType.INVALID_FORMAT);
    }

}