package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.entity.QueryTemplateEntity;
import com.siemens.spine.db.repository.QueryTemplateRepository;
import com.siemens.spine.logic.dto.QueryTemplateDTO;
import com.siemens.spine.logic.dto.request.DynamicQueryByNameDTO;
import com.siemens.spine.logic.dto.request.DynamicQueryDTO;
import com.siemens.spine.logic.mapper.DynamicQueryMapper;
import com.siemens.spine.logic.service.DynamicQueryService;
import com.siemens.spine.logic.util.SecurityUtils;
import com.siemens.spine.logic.validator.query.QueryValidationContext;
import com.siemens.spine.logic.validator.query.QueryValidatorFactory;
import com.siemens.spine.logic.validator.query.ValidationResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.sql.SQLException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Dynamic query service implementation
 *
 * <AUTHOR> Le
 * @version 1.0
 * @since 17/06/2025
 */
@ApplicationScoped
@Slf4j
@Transactional
public class DynamicQueryServiceImpl implements DynamicQueryService {

    private final QueryTemplateRepository queryTemplateRepository;

    private final DynamicQueryMapper dynamicQueryMapper;

    private final QueryValidatorFactory queryValidatorFactory;

    @Inject
    public DynamicQueryServiceImpl(QueryTemplateRepository queryTemplateRepository,
                                   DynamicQueryMapper dynamicQueryMapper,
                                   QueryValidatorFactory queryValidatorFactory) {
        this.queryTemplateRepository = queryTemplateRepository;
        this.dynamicQueryMapper = dynamicQueryMapper;
        this.queryValidatorFactory = queryValidatorFactory;
    }

    public QueryTemplateDTO getQueryTemplate(Long templateId) {
        if (templateId == null) {
            throw new IllegalArgumentException("Template ID cannot be null");
        }

        QueryTemplateEntity template = getQueryTemplateEntity(templateId);
        return dynamicQueryMapper.toQueryTemplateDTO(template);
    }

    @Override
    public QueryTemplateDTO getQueryTemplate(String templateName) {
        if (templateName == null || templateName.isEmpty()) {
            throw new IllegalArgumentException("Template name cannot be null or empty");
        }

        QueryTemplateEntity template = getQueryTemplateByName(templateName);
        return dynamicQueryMapper.toQueryTemplateDTO(template);
    }

    public int getTotalCount(String templateName, Map<String, Object> parameters) throws SQLException {
        QueryTemplateEntity template = getQueryTemplateByName(templateName);
        return queryTemplateRepository.getTotalCount(template, parameters);
    }

    @Override
    public Map<String, Object> executeQuerySafe(DynamicQueryDTO dto) throws SQLException {

        final Long templateId = dto.getTemplateId();
        final Map<String, Object> parameters = dto.getParameters();
        Integer page = Math.max(1, dto.getPage());
        Integer size = Math.max(1, dto.getSize());

        QueryTemplateEntity template = getQueryTemplateEntity(templateId);

        return queryTemplateRepository.executeQuerySafe(template, parameters, page, size);

    }

    @Override
    public Map<String, Object> executeQuerySafe(DynamicQueryByNameDTO dto) throws SQLException {
        String templateName = dto.getTemplateName();
        Optional<QueryTemplateEntity> existingTemplate = queryTemplateRepository.findByTemplateName(templateName);

        if (existingTemplate.isEmpty()) {
            throw new IllegalArgumentException("Template with name '" + templateName + "' does not exist");
        }

        QueryTemplateEntity template = existingTemplate.get();
        final Map<String, Object> parameters = dto.getParameters();
        Integer page = Math.max(1, dto.getPage());
        Integer size = Math.max(1, dto.getSize());

        return queryTemplateRepository.executeQuerySafe(template, parameters, page, size);
    }

    @Override
    public Collection<QueryTemplateDTO> getAllTemplates(Long id, String name, String query, String description) {
        QueryTemplateEntity example = buildExampleQueryTemplate(id, name, query, description);
        List<QueryTemplateEntity> entities = queryTemplateRepository.findAllByExample(example);
        return entities.stream().map(dynamicQueryMapper::toQueryTemplateDTO).filter(Objects::nonNull).toList();
    }

    @Override
    public ValidationResult validateQueryTemplate(String template) {
        QueryValidationContext context = QueryValidationContext.builder()
                .queryString(template)
                .build();
        ValidationResult validateResult = queryValidatorFactory.createValidationChain().validate(context);
        log.info("Validation result: {}", validateResult);
        return validateResult;
    }

    @Override
    public QueryTemplateDTO createQueryTemplate(QueryTemplateDTO template) {
        validateQueryTemplate(template);
        Optional<QueryTemplateEntity> existingTemplate = queryTemplateRepository.findByTemplateName(
                template.getName());

        if (existingTemplate.isPresent()) {
            throw new IllegalArgumentException(
                    "Template with name '" + template.getName() + "' already exists");
        }

        ValidationResult validationResult = validateQueryTemplate(template.getQuery());
        if (!validationResult.valid()) {
            throw new IllegalArgumentException("Invalid SQL template: " + validationResult.errorMessage());
        }

        QueryTemplateEntity entity = dynamicQueryMapper.toQueryTemplateEntity(template);
        entity.setCreatedBy(SecurityUtils.getUsernameFromToken());
        entity.setModifiedBy(SecurityUtils.getUsernameFromToken());
        entity = queryTemplateRepository.save(entity);

        return dynamicQueryMapper.toQueryTemplateDTO(entity);
    }

    @Override
    public QueryTemplateDTO updateQueryTemplate(QueryTemplateDTO template) {
        validateQueryTemplate(template);
        Optional<QueryTemplateEntity> existingTemplate = queryTemplateRepository.findById(template.getId());

        QueryTemplateEntity entity = existingTemplate.orElseThrow(
                () -> new IllegalArgumentException("Template with id '" + template.getId() + "' does not exist"));

        if (!template.getName().equals(entity.getTemplateName())) {
            Optional<QueryTemplateEntity> existingTemplateTmp = queryTemplateRepository.findByTemplateName(
                    template.getName());

            if (existingTemplateTmp.isPresent()) {
                throw new IllegalArgumentException(
                        "Template with name '" + template.getName() + "' already exists");
            }
        }

        ValidationResult validationResult = validateQueryTemplate(template.getQuery());
        if (!validationResult.valid()) {
            throw new IllegalArgumentException("Invalid SQL template: " + validationResult.errorMessage());
        }
        entity.setModifiedBy(SecurityUtils.getUsernameFromToken());
        entity.setDescription(template.getDescription());
        entity.setRequiresPagination(template.isRequiresPagination());
        entity.setTemplateName(template.getName());
        entity.setSqlTemplate(template.getQuery());

        entity = queryTemplateRepository.save(entity);

        return dynamicQueryMapper.toQueryTemplateDTO(entity);
    }

    @Override
    public void deleteQueryTemplate(Long templateId) {
        queryTemplateRepository.deleteById(templateId);
    }

    private QueryTemplateEntity getQueryTemplateEntity(Long templateId) {
        if (templateId == null) {
            throw new IllegalArgumentException("Template ID cannot be null");
        }

        Optional<QueryTemplateEntity> entity = queryTemplateRepository.findById(templateId);
        if (entity.isEmpty()) {
            throw new IllegalArgumentException("Template ID " + templateId + " not found");
        }

        return entity.get();
    }

    private QueryTemplateEntity getQueryTemplateByName(String templateName) {
        if (templateName == null || templateName.isEmpty()) {
            throw new IllegalArgumentException("Template name cannot be null or empty");
        }
        Optional<QueryTemplateEntity> entity = queryTemplateRepository.findByTemplateName(templateName);
        if (entity.isEmpty()) {
            throw new IllegalArgumentException("Template with name '" + templateName + "' not found");
        }
        return entity.get();
    }

    private void validateQueryTemplate(QueryTemplateDTO template) {
        if (template == null
                || StringUtils.isEmpty(template.getQuery())
                || StringUtils.isEmpty(template.getQuery())) {
            log.error("Template or SQL template cannot be null");
            throw new IllegalArgumentException("Template or SQL template cannot be null");
        }
    }

    private QueryTemplateEntity buildExampleQueryTemplate(Long id, String name, String query, String description) {
        QueryTemplateEntity example = new QueryTemplateEntity();
        example.setId(id);
        example.setTemplateName(name);
        example.setSqlTemplate(query);
        example.setDescription(description);
        return example;
    }

}
