package com.siemens.spine.logic.dto.request;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siemens.spine.logic.anotation.Trim;
import java.sql.Timestamp;
import java.util.Map;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CreateQueryTemplateDTO {

  @JsonAlias({"template_name", "templateName"})
  @Trim
  private String templateName;

  @JsonAlias({"sqlTemplate", "sql_template"})
  @Trim
  private String sqlTemplate;

  private String description;

  @JsonAlias({"requiresPagination", "requires_pagination"})
  private boolean requiresPagination = true;

}