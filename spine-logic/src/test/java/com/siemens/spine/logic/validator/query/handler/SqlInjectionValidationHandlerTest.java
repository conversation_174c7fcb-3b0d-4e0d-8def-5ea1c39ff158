package com.siemens.spine.logic.validator.query.handler;

import com.siemens.spine.logic.validator.query.QueryValidationContext;
import com.siemens.spine.logic.validator.query.ValidationErrorType;
import com.siemens.spine.logic.validator.query.ValidationResult;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

/**
 * TDD Test Suite for SqlInjectionValidationHandler
 * <p>
 * This test class follows Test-Driven Development principles to validate
 * SQL injection prevention and security validation logic.
 *
 * <AUTHOR> Refactoring
 * @version 1.0
 * @since 26/06/2025
 */
@DisplayName("SQL Injection Validation Handler TDD Tests")
class SqlInjectionValidationHandlerTDDTest {

    private SqlInjectionValidationHandler handler;

    @BeforeEach
    void setUp() {
        handler = new SqlInjectionValidationHandler();
    }

    @Nested
    @DisplayName("Valid SQL Statements - Should Pass Security Validation")
    class ValidSqlStatementsTests {

        @ParameterizedTest
        @DisplayName("Should allow basic SQL statements")
        @ValueSource(strings = {
                "SELECT id, name FROM users WHERE id = 1",
                "SELECT u.name, p.title FROM users u JOIN posts p ON u.id = p.user_id WHERE u.active = true",
                "UPDATE users SET name = 'John Doe' WHERE id = 1",
                "DELETE FROM users WHERE id = 1 AND active = false"
        })
        void shouldAllowBasicSqlStatements(String sql) {
            // Arrange
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            // Act
            ValidationResult result = handler.validate(context);

            // Assert
            assertTrue(result.valid(), "SQL statement should be allowed: " + sql);
            assertNull(result.errorMessage(), "No error message expected for valid SQL: " + sql);
        }

        @Test
        @DisplayName("Should allow INSERT statement")
        void shouldAllowInsertStatement() {
            String sql = "INSERT INTO users (name, email) VALUES ('John', '<EMAIL>')";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertTrue(result.valid(), "INSERT statement should be allowed");
            assertNull(result.errorMessage());
        }

        @ParameterizedTest
        @DisplayName("Should allow parameterized queries")
        @ValueSource(strings = {
                "SELECT * FROM users WHERE id = ?",
                "UPDATE users SET name = ? WHERE id = ?",
                "DELETE FROM users WHERE id = ? AND status = ?",
                "INSERT INTO users (name, email) VALUES (?, ?)"
        })
        void shouldAllowParameterizedQueries(String sql) {
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertTrue(result.valid(), "Parameterized query should be allowed: " + sql);
            assertNull(result.errorMessage());
        }

    }

    @Nested
    @DisplayName("DDL Statement Detection - Should Block DDL Operations")
    class DdlStatementDetectionTests {

        @ParameterizedTest
        @DisplayName("Should block DROP statements")
        @ValueSource(strings = {
                "DROP TABLE users",
                "DROP INDEX idx_users_email",
                "DROP VIEW user_view"
        })
        void shouldBlockDropStatements(String sql) {
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertFalse(result.valid(), "DROP statement should be blocked: " + sql);
            assertEquals(ValidationErrorType.SECURITY_VIOLATION, result.errorType());
            assertTrue(result.errorMessage().contains("DDL statements are not allowed"),
                    "Error message should indicate DDL restriction");
        }

        @Test
        @DisplayName("Should block invalid DROP DATABASE statement")
        void shouldBlockInvalidDropDatabaseStatement() {
            String sql = "DROP DATABASE testdb";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertFalse(result.valid(), "DROP DATABASE should be blocked");
            assertEquals(ValidationErrorType.SECURITY_VIOLATION, result.errorType());
            assertTrue(result.errorMessage().contains("Query parsing failed"),
                    "Error message should indicate parsing failure for invalid syntax");
        }

        @ParameterizedTest
        @DisplayName("Should block ALTER statements")
        @ValueSource(strings = {
                "ALTER TABLE users ADD COLUMN age INT",
                "ALTER TABLE users DROP COLUMN email",
                "ALTER TABLE users MODIFY COLUMN name VARCHAR(100)"
        })
        void shouldBlockAlterStatements(String sql) {
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertFalse(result.valid(), "ALTER statement should be blocked: " + sql);
            assertEquals(ValidationErrorType.SECURITY_VIOLATION, result.errorType());
            assertTrue(result.errorMessage().contains("DDL statements are not allowed"));
        }

        @ParameterizedTest
        @DisplayName("Should block CREATE statements")
        @ValueSource(strings = {
                "CREATE TABLE new_users (id INT, name VARCHAR(50))",
                "CREATE INDEX idx_name ON users(name)",
                "CREATE VIEW active_users AS SELECT * FROM users WHERE active = true"
        })
        void shouldBlockCreateStatements(String sql) {
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertFalse(result.valid(), "CREATE statement should be blocked: " + sql);
            assertEquals(ValidationErrorType.SECURITY_VIOLATION, result.errorType());
            assertTrue(result.errorMessage().contains("DDL statements are not allowed"));
        }

        @Test
        @DisplayName("Should block TRUNCATE statements")
        void shouldBlockTruncateStatements() {
            String sql = "TRUNCATE TABLE users";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertFalse(result.valid(), "TRUNCATE statement should be blocked");
            assertEquals(ValidationErrorType.SECURITY_VIOLATION, result.errorType());
            assertTrue(result.errorMessage().contains("DDL statements are not allowed"));
        }

    }

    @Nested
    @DisplayName("Multiple Statement Detection - Should Block SQL Injection via Statement Chaining")
    class MultipleStatementDetectionTests {

        @Test
        @DisplayName("Should block multiple statements separated by semicolon")
        void shouldBlockMultipleStatements() {
            String sql = "SELECT * FROM users; DROP TABLE users;";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertFalse(result.valid(), "Multiple statements should be blocked");
            assertEquals(ValidationErrorType.SECURITY_VIOLATION, result.errorType());
            assertTrue(result.errorMessage().contains("Multiple statements are not allowed"),
                    "Error message should indicate multiple statement restriction");
        }

        @ParameterizedTest
        @DisplayName("Should block classic SQL injection attempts")
        @ValueSource(strings = {
                "SELECT * FROM users WHERE id = 1; DELETE FROM users;",
                "SELECT * FROM users; INSERT INTO admin_users VALUES ('hacker', 'password');",
                "UPDATE users SET name = 'test'; DROP TABLE sensitive_data;"
        })
        void shouldBlockClassicSqlInjectionAttempts(String sql) {
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertFalse(result.valid(), "SQL injection attempt should be blocked: " + sql);
            assertEquals(ValidationErrorType.SECURITY_VIOLATION, result.errorType());
            assertTrue(result.errorMessage().contains("Multiple statements are not allowed"));
        }

        @Test
        @DisplayName("Should allow single statement with semicolon at end")
        void shouldAllowSingleStatementWithTrailingSemicolon() {
            String sql = "SELECT * FROM users WHERE id = 1;";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertTrue(result.valid(), "Single statement with trailing semicolon should be allowed");
            assertNull(result.errorMessage());
        }

    }

    @Nested
    @DisplayName("Dangerous Pattern Detection - Should Block Unsafe Operations")
    class DangerousPatternDetectionTests {

        @Test
        @DisplayName("Should block UPDATE without WHERE clause")
        void shouldBlockUpdateWithoutWhereClause() {
            String sql = "UPDATE users SET name = 'John Doe'";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertFalse(result.valid(), "UPDATE without WHERE clause should be blocked");
            assertEquals(ValidationErrorType.SECURITY_VIOLATION, result.errorType());
            assertTrue(result.errorMessage().contains("UPDATE without WHERE clause is not allowed"),
                    "Error message should indicate UPDATE without WHERE restriction");
        }

        @Test
        @DisplayName("Should block DELETE without WHERE clause")
        void shouldBlockDeleteWithoutWhereClause() {
            String sql = "DELETE FROM users";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertFalse(result.valid(), "DELETE without WHERE clause should be blocked");
            assertEquals(ValidationErrorType.SECURITY_VIOLATION, result.errorType());
            assertTrue(result.errorMessage().contains("DELETE without WHERE clause is not allowed"),
                    "Error message should indicate DELETE without WHERE restriction");
        }

        @ParameterizedTest
        @DisplayName("Should block dangerous functions")
        @CsvSource({
                "'SELECT LOAD_FILE(''/etc/passwd'') FROM users', 'LOAD_FILE'",
                "'SELECT * FROM users WHERE id = SLEEP(5)', 'SLEEP'",
                "'SELECT BENCHMARK(1000000, MD5(''test'')) FROM users', 'BENCHMARK'",
                "'SELECT * FROM users WHERE EXEC(''malicious_code'')', 'EXEC'"
        })
        void shouldBlockDangerousFunctions(String sql, String expectedFunction) {
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertFalse(result.valid(), "Query with dangerous function should be blocked: " + sql);
            assertEquals(ValidationErrorType.SECURITY_VIOLATION, result.errorType());
            assertTrue(result.errorMessage().contains("potentially dangerous functions"),
                    "Error message should indicate dangerous function restriction");
        }

    }

    @Nested
    @DisplayName("Edge Cases and Error Handling")
    class EdgeCasesAndErrorHandlingTests {

        @Test
        @DisplayName("Should handle SQL with special characters")
        void shouldHandleSqlWithSpecialCharacters() {
            String sql = "SELECT * FROM users WHERE name = 'O''Brien' AND comment LIKE '%test%'";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertTrue(result.valid(), "SQL with properly escaped special characters should be allowed");
            assertNull(result.errorMessage());
        }

    }

    @Nested
    @DisplayName("Integration Tests - Full Validation Chain")
    class IntegrationTests {

        @Test
        @DisplayName("Should validate complex SELECT with subqueries")
        void shouldValidateComplexSelectWithSubqueries() {
            String sql = "SELECT u.name, (SELECT COUNT(*) FROM posts p WHERE p.user_id = u.id) as post_count " +
                    "FROM users u WHERE u.id IN (SELECT user_id FROM active_sessions WHERE last_activity > NOW() - INTERVAL '1 hour')";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.validate(context);

            assertTrue(result.valid(), "Complex SELECT with subqueries should be allowed");
            assertNull(result.errorMessage());
        }

        @Test
        @DisplayName("Should handle pre-parsed statement in context")
        void shouldHandlePreParsedStatementInContext() {
            String sql = "SELECT * FROM users WHERE id = 1";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            try {
                Statement parsedStatement = CCJSqlParserUtil.parse(sql);
                context.setParsedStatement(parsedStatement);
            } catch (Exception e) {
                fail("Should be able to parse valid SQL");
            }

            ValidationResult result = handler.validate(context);

            assertTrue(result.valid(), "Pre-parsed valid statement should be allowed");
            assertNull(result.errorMessage());
        }

    }

}
