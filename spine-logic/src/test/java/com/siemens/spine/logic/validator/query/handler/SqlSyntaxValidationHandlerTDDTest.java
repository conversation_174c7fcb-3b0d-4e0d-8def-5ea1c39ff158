package com.siemens.spine.logic.validator.query.handler;

import com.siemens.spine.logic.validator.query.QueryValidationContext;
import com.siemens.spine.logic.validator.query.ValidationResult;
import com.siemens.spine.logic.validator.query.ValidationErrorType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.jupiter.params.provider.CsvSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test-Driven Development (TDD) test suite for SqlSyntaxValidationHandler
 * 
 * This test class follows TDD principles:
 * 1. Write failing tests first (Red)
 * 2. Write minimal code to make tests pass (Green) 
 * 3. Refactor while keeping tests green (Refactor)
 * 
 * Test organization follows the AAA pattern:
 * - Arrange: Set up test data and conditions
 * - Act: Execute the method under test
 * - Assert: Verify the expected outcomes
 * 
 * IMPORTANT NOTE ABOUT JSQLPARSER BEHAVIOR:
 * JSQLParser is more permissive than strict SQL standards and may accept
 * some queries that would be rejected by actual databases. This is documented
 * in the test cases where applicable.
 */
@DisplayName("SqlSyntaxValidationHandler TDD Tests")
class SqlSyntaxValidationHandlerTDDTest {

    private SqlSyntaxValidationHandler handler;

    @BeforeEach
    void setUp() {
        handler = new SqlSyntaxValidationHandler();
    }

    @Nested
    @DisplayName("Valid SQL Syntax Tests")
    class ValidSqlSyntaxTests {

        @Test
        @DisplayName("Should validate basic SELECT statement")
        void shouldValidateBasicSelectStatement() {
            // Arrange
            String sql = "SELECT id, name FROM users";
            QueryValidationContext context = QueryValidationContext.builder()
                .queryString(sql)
                .build();

            // Act
            ValidationResult result = handler.validate(context);

            // Assert
            assertTrue(result.valid(), "Basic SELECT should be valid");
            assertNull(result.errorMessage(), "No error message expected for valid SQL");
            assertNotNull(context.getParsedStatement(), "Parsed statement should be set in context");
        }

        @Test
        @DisplayName("Should validate SELECT with WHERE clause")
        void shouldValidateSelectWithWhere() {
            // Arrange
            String sql = "SELECT * FROM users WHERE id = 1";
            QueryValidationContext context = QueryValidationContext.builder()
                .queryString(sql)
                .build();

            // Act
            ValidationResult result = handler.validate(context);

            // Assert
            assertTrue(result.valid(), "SELECT with WHERE should be valid");
            assertNull(result.errorMessage());
            assertNotNull(context.getParsedStatement());
        }

        @ParameterizedTest
        @DisplayName("Should validate various JOIN types")
        @ValueSource(strings = {
            "SELECT u.id, p.name FROM users u INNER JOIN profiles p ON u.id = p.user_id",
            "SELECT u.id, p.name FROM users u LEFT JOIN profiles p ON u.id = p.user_id",
            "SELECT u.id, p.name FROM users u RIGHT JOIN profiles p ON u.id = p.user_id",
            "SELECT u.id, p.name FROM users u FULL OUTER JOIN profiles p ON u.id = p.user_id",
            "SELECT u.id, p.name FROM users u CROSS JOIN profiles p"
        })
        void shouldValidateJoinTypes(String sql) {
            // Arrange
            QueryValidationContext context = QueryValidationContext.builder()
                .queryString(sql)
                .build();

            // Act
            ValidationResult result = handler.validate(context);

            // Assert
            assertTrue(result.valid(), "JOIN query should be valid: " + sql);
            assertNull(result.errorMessage());
        }

        @ParameterizedTest
        @DisplayName("Should validate complex SELECT features")
        @ValueSource(strings = {
            "SELECT COUNT(*) FROM users GROUP BY department",
            "SELECT * FROM users ORDER BY name ASC",
            "SELECT * FROM users ORDER BY name DESC",
            "SELECT * FROM users LIMIT 10",
            "SELECT * FROM users LIMIT 10 OFFSET 5",
            "SELECT COUNT(*) FROM users GROUP BY department HAVING COUNT(*) > 5"
        })
        void shouldValidateComplexSelectFeatures(String sql) {
            // Arrange
            QueryValidationContext context = QueryValidationContext.builder()
                .queryString(sql)
                .build();

            // Act
            ValidationResult result = handler.validate(context);

            // Assert
            assertTrue(result.valid(), "Complex SELECT should be valid: " + sql);
            assertNull(result.errorMessage());
        }
    }

    @Nested
    @DisplayName("Invalid SQL Syntax Tests")
    class InvalidSqlSyntaxTests {

        @Test
        @DisplayName("Should reject completely malformed SQL")
        void shouldRejectMalformedSql() {
            // Arrange
            String sql = "INVALID SQL STATEMENT";
            QueryValidationContext context = QueryValidationContext.builder()
                .queryString(sql)
                .build();

            // Act
            ValidationResult result = handler.validate(context);

            // Assert
            assertFalse(result.valid(), "Malformed SQL should be invalid");
            assertNotNull(result.errorMessage(), "Error message should be provided");
            assertTrue(result.errorMessage().contains("Invalid SQL syntax"), 
                "Error message should indicate syntax error");
            assertEquals(ValidationErrorType.INVALID_SYNTAX, result.errorType());
        }

        @ParameterizedTest
        @DisplayName("Should reject SQL with clear syntax errors")
        @CsvSource({
            "'SELECT FROM WHERE', 'Missing table name'",
            "'SELECT * FROM', 'Incomplete FROM clause'",
            "'SELECT * FROM users WHERE', 'Incomplete WHERE clause'"
        })
        void shouldRejectSqlWithSyntaxErrors(String sql, String description) {
            // Arrange
            QueryValidationContext context = QueryValidationContext.builder()
                .queryString(sql)
                .build();

            // Act
            ValidationResult result = handler.validate(context);

            // Assert
            assertFalse(result.valid(), description + " should be invalid: " + sql);
            assertNotNull(result.errorMessage(), "Error message should be provided for: " + description);
            assertEquals(ValidationErrorType.INVALID_SYNTAX, result.errorType());
        }

        @Test
        @DisplayName("Should document JSQLParser permissive behavior")
        void shouldDocumentJSqlParserPermissiveBehavior() {
            // JSQLParser may accept some non-standard SQL that would fail in real databases
            String[] problematicQueries = {
                "SELECT * WHERE id = 1",  // Missing FROM clause
                "SELECT asc WHERE projectId = xyz"  // Unquoted identifier, missing FROM
            };

            for (String sql : problematicQueries) {
                QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();
                ValidationResult result = handler.validate(context);

                // Document actual behavior - JSQLParser may be permissive
                if (result.valid()) {
                    System.out.println("JSQLParser allows non-standard SQL: " + sql);
                    assertNull(result.errorMessage());
                } else {
                    assertNotNull(result.errorMessage());
                    assertTrue(result.errorMessage().contains("Invalid SQL syntax"));
                    System.out.println("JSQLParser correctly rejected: " + sql);
                }
            }
        }
    }

    @Nested
    @DisplayName("Edge Cases and Boundary Tests")
    class EdgeCasesTests {

        @Test
        @DisplayName("Should handle null query string")
        void shouldHandleNullQueryString() {
            // Arrange
            QueryValidationContext context = QueryValidationContext.builder()
                .queryString(null)
                .build();

            // Act & Assert
            assertThrows(IllegalArgumentException.class, 
                () -> handler.validate(context),
                "Null query string should throw IllegalArgumentException");
        }

        @Test
        @DisplayName("Should handle empty query string")
        void shouldHandleEmptyQueryString() {
            // Arrange
            QueryValidationContext context = QueryValidationContext.builder()
                .queryString("")
                .build();

            // Act & Assert
            assertThrows(IllegalArgumentException.class, 
                () -> handler.validate(context),
                "Empty query string should throw IllegalArgumentException");
        }

        @Test
        @DisplayName("Should handle whitespace-only query string")
        void shouldHandleWhitespaceOnlyQueryString() {
            // Arrange
            QueryValidationContext context = QueryValidationContext.builder()
                .queryString("   \t\n   ")
                .build();

            // Act & Assert
            assertThrows(IllegalArgumentException.class, 
                () -> handler.validate(context),
                "Whitespace-only query string should throw IllegalArgumentException");
        }

        @Test
        @DisplayName("Should handle very long SQL statement")
        void shouldHandleVeryLongSqlStatement() {
            // Arrange
            StringBuilder longSql = new StringBuilder("SELECT ");
            for (int i = 1; i <= 1000; i++) {
                longSql.append("column").append(i);
                if (i < 1000) longSql.append(", ");
            }
            longSql.append(" FROM large_table");

            QueryValidationContext context = QueryValidationContext.builder()
                .queryString(longSql.toString())
                .build();

            // Act
            ValidationResult result = handler.validate(context);

            // Assert
            assertTrue(result.valid(), "Long SQL statement should be valid");
            assertNull(result.errorMessage());
        }
    }

    @Nested
    @DisplayName("PostgreSQL Specific Syntax Tests")
    class PostgreSqlSpecificTests {

        @ParameterizedTest
        @DisplayName("Should validate PostgreSQL-specific syntax")
        @ValueSource(strings = {
            "SELECT * FROM users WHERE name ILIKE '%john%'",
            "SELECT * FROM users LIMIT ALL",
            "SELECT DISTINCT ON (department) * FROM users",
            "SELECT * FROM users WHERE id = ANY(ARRAY[1,2,3])"
        })
        void shouldValidatePostgreSqlSyntax(String sql) {
            // Arrange
            QueryValidationContext context = QueryValidationContext.builder()
                .queryString(sql)
                .build();

            // Act
            ValidationResult result = handler.validate(context);

            // Assert
            assertTrue(result.valid(), "PostgreSQL syntax should be valid: " + sql);
            assertNull(result.errorMessage());
        }
    }
}
