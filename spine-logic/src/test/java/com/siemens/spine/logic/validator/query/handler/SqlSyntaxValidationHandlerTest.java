package com.siemens.spine.logic.validator.query.handler;

import com.siemens.spine.logic.validator.query.QueryValidationContext;
import com.siemens.spine.logic.validator.query.ValidationErrorType;
import com.siemens.spine.logic.validator.query.ValidationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Test-Driven Development (TDD) test suite for SqlSyntaxValidationHandler
 * <p>
 * This test class follows TDD principles:
 * 1. Write failing tests first (Red)
 * 2. Write minimal code to make tests pass (Green)
 * 3. Refactor while keeping tests green (Refactor)
 * <p>
 * Test organization follows the AAA pattern:
 * - Arrange: Set up test data and conditions
 * - Act: Execute the method under test
 * - Assert: Verify the expected outcomes
 */
@DisplayName("SqlSyntaxValidationHandler TDD Tests")
class SqlSyntaxValidationHandlerTest {

    private SqlSyntaxValidationHandler handler;

    @BeforeEach
    void setUp() {
        handler = new SqlSyntaxValidationHandler();
    }

    @Nested
    @DisplayName("Valid SQL Syntax Tests")
    class ValidSqlSyntaxTests {

        @Test
        @DisplayName("Should validate basic SELECT statement")
        void shouldValidateBasicSelectStatement() {
            // Arrange
            String sql = "SELECT id, name FROM users";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            // Act
            ValidationResult result = handler.doValidate(context);

            // Assert
            assertTrue(result.valid(), "Basic SELECT should be valid");
            assertNull(result.errorMessage(), "No error message expected for valid SQL");
            assertNotNull(context.getParsedStatement(), "Parsed statement should be set in context");
        }

        @Test
        @DisplayName("Should validate SELECT with WHERE clause")
        void shouldValidateSelectWithWhere() {
            // Arrange
            String sql = "SELECT * FROM users WHERE id = 1";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            // Act
            ValidationResult result = handler.doValidate(context);

            // Assert
            assertTrue(result.valid(), "SELECT with WHERE should be valid");
            assertNull(result.errorMessage());
            assertNotNull(context.getParsedStatement());
        }

        @ParameterizedTest
        @DisplayName("Should validate various JOIN types")
        @ValueSource(strings = {
                "SELECT u.id, p.name FROM users u INNER JOIN profiles p ON u.id = p.user_id",
                "SELECT u.id, p.name FROM users u LEFT JOIN profiles p ON u.id = p.user_id",
                "SELECT u.id, p.name FROM users u RIGHT JOIN profiles p ON u.id = p.user_id",
                "SELECT u.id, p.name FROM users u FULL OUTER JOIN profiles p ON u.id = p.user_id",
                "SELECT u.id, p.name FROM users u CROSS JOIN profiles p"
        })
        void shouldValidateJoinTypes(String sql) {
            // Arrange
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            // Act
            ValidationResult result = handler.doValidate(context);

            // Assert
            assertTrue(result.valid(), "JOIN query should be valid: " + sql);
            assertNull(result.errorMessage());
        }

        @ParameterizedTest
        @DisplayName("Should validate complex SELECT features")
        @ValueSource(strings = {
                "SELECT COUNT(*) FROM users GROUP BY department",
                "SELECT * FROM users ORDER BY name ASC",
                "SELECT * FROM users ORDER BY name DESC",
                "SELECT * FROM users LIMIT 10",
                "SELECT * FROM users LIMIT 10 OFFSET 5",
                "SELECT COUNT(*) FROM users GROUP BY department HAVING COUNT(*) > 5"
        })
        void shouldValidateComplexSelectFeatures(String sql) {
            // Arrange
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            // Act
            ValidationResult result = handler.doValidate(context);

            // Assert
            assertTrue(result.valid(), "Complex SELECT should be valid: " + sql);
            assertNull(result.errorMessage());
        }

    }

    @Nested
    @DisplayName("Invalid SQL Syntax Tests")
    class InvalidSqlSyntaxTests {

        @Test
        @DisplayName("Should reject completely malformed SQL")
        void shouldRejectMalformedSql() {
            String sql = "INVALID SQL STATEMENT";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.doValidate(context);

            assertFalse(result.valid(), "Malformed SQL should be invalid");
            assertNotNull(result.errorMessage(), "Error message should be provided");
            assertTrue(result.errorMessage().contains("Invalid SQL syntax"),
                    "Error message should indicate syntax error");
            assertEquals(ValidationErrorType.INVALID_SYNTAX, result.errorType());
        }

        @ParameterizedTest
        @DisplayName("Should reject SQL with syntax errors")
        @CsvSource({
                "'SELECT FROM WHERE', 'Missing table name'",
                "'SELECT * FROM', 'Incomplete FROM clause'",
                "'SELECT * FROM users WHERE', 'Incomplete WHERE clause'"
        })
        void shouldRejectSqlWithSyntaxErrors(String sql, String description) {
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.doValidate(context);

            assertFalse(result.valid(), description + " should be invalid: " + sql);
            assertNotNull(result.errorMessage(), "Error message should be provided for: " + description);
            assertEquals(ValidationErrorType.INVALID_SYNTAX, result.errorType());
        }

        @Test
        @DisplayName("Should document JSQLParser permissive behavior for SELECT without FROM")
        void shouldDocumentJSqlParserPermissiveBehavior() {
            // JSQLParser allows "SELECT * WHERE id = 1" even though it violates SQL standards
            String problematicSql = "SELECT * WHERE id = 1";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(problematicSql)
                    .build();

            ValidationResult result = handler.doValidate(context);
            if (result.valid()) {
                assertNull(result.errorMessage());
            } else {
                assertNotNull(result.errorMessage());
                assertTrue(result.errorMessage().contains("Invalid SQL syntax"));
            }
        }

    }

    @Nested
    @DisplayName("PostgreSQL Specific Syntax Tests")
    class PostgreSqlSpecificTests {

        @ParameterizedTest
        @DisplayName("Should validate PostgreSQL-specific syntax")
        @ValueSource(strings = {
                "SELECT * FROM users WHERE name ILIKE '%john%'",
                "SELECT * FROM users LIMIT ALL",
                "SELECT DISTINCT ON (department) * FROM users",
                "SELECT * FROM users WHERE id = ANY(ARRAY[1,2,3])"
        })
        void shouldValidatePostgreSqlSyntax(String sql) {
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.doValidate(context);

            assertTrue(result.valid(), "PostgreSQL syntax should be valid: " + sql);
            assertNull(result.errorMessage());
        }

    }

    @Nested
    @DisplayName("SQL Injection Prevention Tests")
    class SqlInjectionPreventionTests {

        @Test
        @DisplayName("Should validate SQL with proper parameterization")
        void shouldValidateSqlWithProperParameterization() {
            String sql = "SELECT * FROM users WHERE id = ? AND name = ?";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.doValidate(context);

            assertTrue(result.valid(), "Parameterized SQL should be valid");
            assertNull(result.errorMessage());
        }

        @Test
        @DisplayName("Should handle complex nested queries")
        void shouldHandleComplexNestedQueries() {
            String sql = "SELECT u.id, u.name FROM users u WHERE u.department_id IN " +
                    "(SELECT d.id FROM departments d WHERE d.active = true)";
            QueryValidationContext context = QueryValidationContext.builder()
                    .queryString(sql)
                    .build();

            ValidationResult result = handler.doValidate(context);

            assertTrue(result.valid(), "Complex nested query should be valid");
            assertNull(result.errorMessage());
        }

    }

}
