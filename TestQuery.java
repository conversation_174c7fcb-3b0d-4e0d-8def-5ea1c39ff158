import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;

public class TestQuery {
    public static void main(String[] args) {
        String query = "SELECT asc where projectId = xyz";
        try {
            Statement statement = CCJSqlParserUtil.parse(query);
            System.out.println("Query parsed successfully: " + statement);
        } catch (JSQLParserException e) {
            System.out.println("Parse error: " + e.getMessage());
        }
    }
}
