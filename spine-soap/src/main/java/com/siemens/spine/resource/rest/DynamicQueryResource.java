package com.siemens.spine.resource.rest;

import com.siemens.spine.logic.dto.QueryTemplateDTO;
import com.siemens.spine.logic.dto.request.DynamicQueryByNameDTO;
import com.siemens.spine.logic.dto.request.DynamicQueryCountDTO;
import com.siemens.spine.logic.dto.request.DynamicQueryDTO;
import com.siemens.spine.logic.service.DynamicQueryService;
import com.siemens.spine.logic.validator.query.ValidationResult;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.sql.SQLException;
import java.util.Map;

@Slf4j
@Path("/query-templates")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequestScoped
public class DynamicQueryResource {

    private final DynamicQueryService dynamicQueryService;

    @Inject
    public DynamicQueryResource(DynamicQueryService dynamicQueryService) {
        this.dynamicQueryService = dynamicQueryService;
    }

    @GET
    public Response getAllTemplates(@QueryParam("id") Long id,
                                    @QueryParam("name") String name,
                                    @QueryParam("query") String query,
                                    @QueryParam("description") String description) {
        return Response.ok(dynamicQueryService.getAllTemplates(id, name, query, description)).build();
    }

    @GET
    @Path("/{id}")
    public Response getTemplate(@PathParam("id") Long id) {
        QueryTemplateDTO dto = dynamicQueryService.getQueryTemplate(id);
        if (dto == null) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }
        return Response.ok(dto).build();
    }

    @POST
    public Response createTemplate(QueryTemplateDTO dto) throws SQLException {
        QueryTemplateDTO created = dynamicQueryService.createQueryTemplate(dto);
        return Response.status(Response.Status.CREATED).entity(created).build();
    }

    @PUT
    @Path("/{id}")
    public Response updateTemplate(@PathParam("id") Long id, QueryTemplateDTO dto) throws SQLException {
        if (dto.getId() != null && !id.equals(dto.getId())) {
            throw new IllegalArgumentException("id must be equal to id");
        }
        QueryTemplateDTO updated = dynamicQueryService.updateQueryTemplate(dto);
        return Response.ok(updated).build();
    }

    @DELETE
    @Path("/{id}")
    public Response deleteTemplate(@PathParam("id") Long id) throws SQLException {
        dynamicQueryService.deleteQueryTemplate(id);
        return Response.ok().build();
    }

    @POST
    @Path("/execute/by-id")
    public Response executeQuerySafe(@Valid DynamicQueryDTO dto) throws SQLException {

        Map<String, Object> result = dynamicQueryService.executeQuerySafe(dto);
        return Response.ok(result).build();
    }

    @POST
    @Path("/execute/by-name")
    public Response executeQuerySafe(@Valid DynamicQueryByNameDTO dto) throws SQLException {

        Map<String, Object> result = dynamicQueryService.executeQuerySafe(dto);
        return Response.ok(result).build();
    }

    @POST
    @Path("/count/by-name")
    public Response getTotalCount(
            DynamicQueryCountDTO dto) throws SQLException {
        if (dto.getTemplateName() == null) {
            throw new IllegalArgumentException("templateName is null");
        }
        int count = dynamicQueryService.getTotalCount(dto.getTemplateName(), dto.getParameters());
        return Response.ok(Map.of("totalCount", count)).build();
    }

    @POST
    @Path("/validate")
    public Response validateSafeQuery(QueryTemplateDTO template) throws SQLException {
        ValidationResult validationResult = dynamicQueryService.validateQueryTemplate(template.getQuery());
        return Response.ok(validationResult).build();
    }

}
