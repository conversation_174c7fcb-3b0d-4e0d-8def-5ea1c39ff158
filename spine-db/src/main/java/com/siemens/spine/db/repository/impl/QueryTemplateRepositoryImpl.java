package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.QueryTemplateEntity;
import com.siemens.spine.db.repository.QueryTemplateRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Example;
import org.postgresql.util.PSQLException;
import org.postgresql.util.ServerErrorMessage;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.NoResultException;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.sql.DataSource;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@ApplicationScoped
@Transactional(TxType.REQUIRED)
@Slf4j
public class QueryTemplateRepositoryImpl extends
        GenericJpaAuditRepositoryImpl<QueryTemplateEntity, Long> implements QueryTemplateRepository {

    DataSource dataSource;

    @Override
    public Class<QueryTemplateEntity> getDomainType() {
        return QueryTemplateEntity.class;
    }

    @Override
    public Optional<QueryTemplateEntity> findByTemplateName(String templateName) {
        try {
            if (StringUtils.isBlank(templateName)) {
                throw new IllegalArgumentException("Template name cannot be null or empty");
            }
            QueryTemplateEntity project = entityManager.createQuery(
                            """
                                    select q
                                    from QueryTemplateEntity q
                                    where q.templateName = :name
                                    """, QueryTemplateEntity.class)
                    .setParameter("name", templateName)
                    .getSingleResult();
            return Optional.of(project);
        } catch (NoResultException ex) {
            return Optional.empty();
        }
    }

    @Override
    public Map<String, Object> executeQuerySafe(final QueryTemplateEntity template,
                                                Map<String, Object> parameters,
                                                Integer page,
                                                Integer size) throws SQLException {

        String finalQuery = buildFinalQuery(template);
        List<Object> queryParams = extractParameterValues(template.getSqlTemplate(), parameters);

        log.info("Executing safe query: {}", finalQuery);
        log.info("Parameters: {}", queryParams);

        Integer totalCount = null;
        if (template.isRequiresPagination()) {
            // Get total count only if pagination is required
            totalCount = getTotalCount(template, parameters);
        }

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<Map<String, Object>> results = new ArrayList<>();

        try {
            conn = dataSource.getConnection();
            conn.setAutoCommit(false);

            boolean usePagination = template.isRequiresPagination();
            int offset = usePagination ? (page - 1) * size : 0;

            stmt = conn.prepareStatement(finalQuery);
            setParameters(stmt, queryParams, usePagination, size, offset);

            rs = stmt.executeQuery();

            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Map<String, Object> row = new LinkedHashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = rs.getObject(i);
                    row.put(columnName, value);
                }
                results.add(row);
            }

            conn.commit();

            if (!template.isRequiresPagination()) {
                totalCount = results.size();
                page = null;
                size = null;
            }

            // Wrap into pagination result
            Map<String, Object> response = new LinkedHashMap<>();
            response.put("page", page);
            response.put("size", size);
            response.put("totalCount", totalCount);
            response.put("data", results);

            return response;
        } catch (PSQLException psqlException) {
            ServerErrorMessage serverError = psqlException.getServerErrorMessage();
            int errorPosition = -1;
            String errorContext = psqlException.getMessage();
            if (serverError != null) {
                errorPosition = serverError.getPosition();
                errorContext = errorContext + ".Detail: " + extractErrorContext(finalQuery, errorPosition);
                log.error("SQL Error at position {}: {}", errorPosition, errorContext);
            } else {
                log.error("SQL Error: {}", psqlException.getMessage());
            }
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) { /* ignore */ }
            }
            throw new RuntimeException(errorContext);
        } catch (SQLException e) {
            log.error("Database error in executeQuerySafe", e);
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) { /* ignore */ }
            }
            throw e;
        } finally {
            closeResource(rs);
            closeResource(stmt);
            closeResource(conn);
        }
    }

    @Override
    public List<QueryTemplateEntity> findAllByExample(QueryTemplateEntity example) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<QueryTemplateEntity> query = cb.createQuery(QueryTemplateEntity.class);
        Root<QueryTemplateEntity> root = query.from(QueryTemplateEntity.class);

        List<Predicate> predicates = new ArrayList<>();
        if (example.getId() != null) {
            predicates.add(cb.equal(root.get("id"), example.getId()));
        }
        if (example.getTemplateName() != null) {
            predicates.add(cb.like(root.get("templateName"), "%" + example.getTemplateName() + "%"));
        }
        if (example.getSqlTemplate() != null) {
            predicates.add(cb.like(root.get("sqlTemplate"), "%" + example.getSqlTemplate() + "%"));
        }
        if (example.getDescription() != null) {
            predicates.add(cb.like(root.get("description"), "%" + example.getDescription() + "%"));
        }

        query.where(predicates.toArray(new Predicate[0]));
        return entityManager.createQuery(query).getResultList();
    }

    @Override
    public int getTotalCount(QueryTemplateEntity template, Map<String, Object> parameters) throws SQLException {

        String countQuery = buildCountQuery(template.getSqlTemplate(), parameters);
        List<Object> queryParams = extractParameterValues(template.getSqlTemplate(), parameters);

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        try {
            conn = dataSource.getConnection();
            stmt = conn.prepareStatement(countQuery);

            setParameters(stmt, queryParams, false, 0, 0);

            rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt(1);
            }
            return 0;
        } catch (PSQLException psqlException) {
            ServerErrorMessage serverError = psqlException.getServerErrorMessage();
            if (serverError != null) {
                int errorPosition = serverError.getPosition();
                String errorContext = extractErrorContext(countQuery, errorPosition);
                log.error("SQL Error at position {}: {}", errorPosition, errorContext);
            } else {
                log.error("SQL Error: {}", psqlException.getMessage());
            }
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) { /* ignore */ }
            }
            throw new RuntimeException("Database error occurred while executing query", psqlException);
        } catch (SQLException e) {
            log.error("Database error in executeQuerySafe", e);
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) { /* ignore */ }
            }
            throw e;
        } finally {
            closeResource(rs);
            closeResource(stmt);
            closeResource(conn);
        }
    }

    public List<Object> extractParameterValues(String query, Map<String, Object> parameters) {
        List<Object> values = new ArrayList<>();
        //        Pattern pattern = Pattern.compile(":(\\w+)");
        Pattern pattern = Pattern.compile("\\[:(\\w+)\\]");

        Matcher matcher = pattern.matcher(query);

        while (matcher.find()) {
            String paramName = matcher.group(1);
            if (parameters.containsKey(paramName)) {
                values.add(parameters.get(paramName));
            }
        }

        return values;
    }

    private String buildFinalQuery(QueryTemplateEntity template) {
        String query = replaceAllPlaceholders(template.getSqlTemplate());

        if (template.isRequiresPagination()) {
            query += " LIMIT ? OFFSET ?";
        }

        return query;
    }

    private String buildCountQuery(String originalQuery, Map<String, Object> parameters) {
        String query = replaceAllPlaceholders(originalQuery);

        // Wrap in subquery to count
        if (query.toLowerCase().contains("select count(")) {
            return query; // Already a count query
        }

        return "SELECT COUNT(*) FROM (" + query + ") AS count_subquery";
    }

    private String replaceAllPlaceholders(String query) {
        Pattern pattern = Pattern.compile("\\[:\\w+\\]");

        Matcher matcher = pattern.matcher(query);
        return matcher.replaceAll("?");
    }

    public static String extractErrorContext(String query, int errorPosition) {

        if (query == null || errorPosition < 0 || errorPosition >= query.length()) {
            return "";
        }

        int start = Math.max(0, errorPosition - 25);
        int end = Math.min(query.length(), errorPosition + 25);

        return "'" + query.substring(start, end) + "'";
    }

    private void setParameters(PreparedStatement stmt, List<Object> params, boolean addPagination, int size, int offset)
            throws SQLException {
        int index = 1;

        // Set dynamic parameters
        for (Object param : params) {
            if (param instanceof String) {
                stmt.setString(index++, (String) param);
            } else if (param instanceof Integer) {
                stmt.setInt(index++, (Integer) param);
            } else if (param instanceof Long) {
                stmt.setLong(index++, (Long) param);
            } else if (param instanceof Date) {
                stmt.setDate(index++, (Date) param);
            } else {
                stmt.setObject(index++, param);
            }
        }

        // Set pagination parameters
        if (addPagination) {
            stmt.setInt(index++, size);   // LIMIT
            stmt.setInt(index, offset);   // OFFSET
        }
    }

    private void closeResource(AutoCloseable resource) {
        if (resource != null) {
            try {
                resource.close();
            } catch (Exception e) {
                // Log the error but don't throw exception
                System.err.println("Error closing resource: " + e.getMessage());
            }
        }
    }

}
